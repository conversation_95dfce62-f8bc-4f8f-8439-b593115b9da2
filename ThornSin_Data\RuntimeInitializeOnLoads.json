{"root": [{"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineStoryboard", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineCore", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "UpdateTracker", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineImpulseManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine.PostFX", "className": "CinemachinePostProcessing", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine.PostFX", "className": "CinemachineVolumeSettings", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Coffee.UIParticle", "nameSpace": "Coffee.UIExtensions", "className": "UIParticleUpdater", "methodName": "InitializeOnLoad", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Unity.RenderPipelines.Universal.Runtime", "nameSpace": "UnityEngine.Rendering.Universal", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "spine-unity", "nameSpace": "Spine.Unity.AttachmentTools", "className": "AtlasUtilities", "methodName": "Init", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "UIEffect", "nameSpace": "Coffee.UIEffects", "className": "GraphicConnector", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "BehaviorDesigner.Runtime", "nameSpace": "BehaviorDesigner.Runtime", "className": "Behavior", "methodName": "DomainReset", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "BehaviorDesigner.Runtime", "nameSpace": "BehaviorDesigner.Runtime", "className": "BehaviorManager", "methodName": "DomainReset", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "BehaviorDesigner.Runtime", "nameSpace": "BehaviorDesigner.Runtime", "className": "GlobalVariables", "methodName": "DomainReset", "loadTypes": 4, "isUnityClass": false}]}