{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.AutoStreamingModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.CloudFoundationModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UIElementsNativeModule.dll", "UnityEngine.UNETModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp.dll", "Unity.InternalAPIEngineBridge.001.dll", "Unity.2D.PixelPerfect.dll", "Unity.RenderPipelines.Universal.Shaders.dll", "Unity.Addressables.dll", "spine-unity-examples.dll", "Unity.InputSystem.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "UnityEngine.UI.dll", "Unity.2D.IK.Runtime.dll", "AllIn1SpriteShaderAssembly.dll", "Cinemachine.dll", "Unity.2D.Animation.Triangle.Runtime.dll", "Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Coffee.UIParticle.dll", "spine-csharp.dll", "Unity.Recorder.dll", "Unity.Recorder.Base.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.VisualEffectGraph.Runtime.dll", "spine-unity.dll", "Unity.Timeline.dll", "Unity.TextMeshPro.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.ResourceManager.dll", "XNode.dll", "Unity.Mathematics.dll", "UIEffect.dll", "Unity.Postprocessing.Runtime.dll", "Unity.2D.Animation.Runtime.dll", "FMODUnity.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Unity.2D.Common.Runtime.dll", "Unity.ScriptableBuildPipeline.dll", "FMODUnityResonance.dll", "Unity.2D.SpriteShape.Runtime.dll", "I18N.MidEast.dll", "I18N.CJK.dll", "I18N.Rare.dll", "Jint.dll", "I18N.Other.dll", "I18N.dll", "BehaviorDesigner.Runtime.dll", "GameAnalytics.dll", "Newtonsoft.Json.dll", "ICSharpCode.SharpZipLib.dll", "I18N.West.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}