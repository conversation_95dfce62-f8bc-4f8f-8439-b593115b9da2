# ThornSin 游戏音频资源提取报告

## 提取概况

### 成功提取的音频文件
- **MUS.bank**: 1019 个音频文件 (主要是音乐和BGM)
- **AMB.bank**: 197 个音频文件 (主要是环境音效和氛围音乐)
- **总计**: 1216 个音频文件

### 文件格式
- 主要格式: MP3
- 部分格式: WAV, FSB

## Secret Passage 关卡BGM分析

### 关键发现
根据FMOD字符串分析，游戏中包含以下相关音频标识：
- `Cave` - 洞穴场景音乐
- `Forest` - 森林场景音乐  
- `Ambience` - 环境氛围音乐
- `Stage_0`, `Stage_03` - 特定关卡音乐
- `Room` - 房间/区域音乐

### Secret Passage BGM推测

基于游戏结构和音频文件分析，Secret Passage关卡的BGM最可能是：

#### 主要候选 (按可能性排序)
1. **Cave相关音乐** - 如果Secret Passage是地下洞穴场景
   - 文件位置: `extracted_audio/MUS/` 或 `extracted_audio/AMB/`
   - 特征: 神秘、幽暗的氛围音乐

2. **Ambience相关音乐** - 环境氛围音效
   - 适合隐秘通道的安静背景音乐
   - 可能包含回声、水滴声等环境音效

3. **Stage特定音乐** - 如果Secret Passage是特定关卡
   - 可能对应 `Stage_0` 或其他编号的关卡音乐

#### 次要候选
4. **Forest相关音乐** - 如果是森林中的秘密通道
5. **Room相关音乐** - 特定房间/区域的背景音乐

### 建议的查找方法

1. **按文件大小筛选**
   - BGM文件通常较大 (>50KB)
   - 音效文件通常较小 (<20KB)

2. **按文件命名模式**
   - 查找包含 "cave", "ambience", "stage" 等关键词的文件
   - 注意文件的偏移地址可能对应特定场景

3. **试听验证**
   - 播放较大的MP3文件
   - 寻找循环播放的背景音乐
   - 识别具有神秘/幽暗氛围的音乐

## 音频文件组织

### MUS.bank (音乐库)
- 包含主要的背景音乐和BGM
- 文件大小范围: 1KB - 200KB+
- 主要格式: MP3

### AMB.bank (环境音效库)  
- 包含环境音效和氛围音乐
- 文件大小范围: 1KB - 200KB+
- 包含一些较大的环境音乐文件

## 下一步建议

1. **手动试听**: 播放提取的音频文件，特别是较大的文件
2. **关键词搜索**: 在文件名中搜索相关关键词
3. **游戏内验证**: 在游戏中进入Secret Passage关卡，对比音频
4. **文件分类**: 根据音频内容对文件进行分类整理

## 技术说明

- 使用Python脚本从FMOD Bank文件中提取音频
- 通过魔数识别不同音频格式
- 保留原始文件的偏移地址信息便于追踪
