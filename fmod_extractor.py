#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FMOD Bank文件分析工具
用于提取ThornSin游戏的音频资源信息
"""

import os
import struct
import sys
from pathlib import Path

def read_fmod_strings(strings_file):
    """读取FMOD字符串文件"""
    strings = []
    try:
        with open(strings_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    strings.append(line)
    except Exception as e:
        print(f"读取字符串文件失败: {e}")
    return strings

def analyze_bank_file(bank_file):
    """分析FMOD bank文件"""
    print(f"\n分析文件: {bank_file}")
    
    try:
        with open(bank_file, 'rb') as f:
            # 读取文件头
            header = f.read(16)
            if len(header) < 16:
                print("文件太小，无法分析")
                return
            
            # 检查FMOD标识
            if header[:4] == b'RIFF':
                print("检测到RIFF格式")
                file_size = struct.unpack('<I', header[4:8])[0]
                print(f"文件大小: {file_size} bytes")
                
                # 读取更多信息
                chunk_type = f.read(4)
                print(f"块类型: {chunk_type}")
                
            elif header[:4] == b'FSB5':
                print("检测到FSB5格式")
                
            else:
                print(f"未知格式，前4字节: {header[:4]}")
                
            # 获取文件大小
            f.seek(0, 2)
            actual_size = f.tell()
            print(f"实际文件大小: {actual_size} bytes")
            
    except Exception as e:
        print(f"分析文件失败: {e}")

def find_music_references(strings):
    """查找音乐相关的引用"""
    music_refs = []
    keywords = ['MUS_', 'BGM', 'Music', 'music', 'Cave', 'Forest', 'Dungeon', 
                'Stage', 'Level', 'Room', 'Scene', 'Ambience', 'AMB_']
    
    for string in strings:
        for keyword in keywords:
            if keyword.lower() in string.lower():
                music_refs.append(string)
                break
    
    return music_refs

def main():
    print("ThornSin FMOD音频资源分析工具")
    print("=" * 50)
    
    # 设置路径
    base_path = Path(".")
    streaming_assets = base_path / "ThornSin_Data" / "StreamingAssets"
    
    # 读取字符串文件
    strings_file = base_path / "fmod_strings.txt"
    strings_all_file = base_path / "fmod_strings_all.txt"
    
    print("\n1. 读取FMOD字符串文件...")
    strings = read_fmod_strings(strings_file)
    strings_all = read_fmod_strings(strings_all_file)
    
    print(f"找到 {len(strings)} 个字符串条目")
    print(f"找到 {len(strings_all)} 个完整字符串条目")
    
    # 查找音乐相关引用
    print("\n2. 查找音乐相关引用...")
    music_refs = find_music_references(strings + strings_all)
    music_refs = list(set(music_refs))  # 去重
    music_refs.sort()
    
    print(f"找到 {len(music_refs)} 个音乐相关引用:")
    for ref in music_refs:
        print(f"  - {ref}")
    
    # 分析bank文件
    print("\n3. 分析FMOD Bank文件...")
    bank_files = list(streaming_assets.glob("*.bank"))
    
    for bank_file in bank_files:
        analyze_bank_file(bank_file)
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
