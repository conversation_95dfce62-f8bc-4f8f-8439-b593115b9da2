<linker>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AchievementData" preserve="all" />
    <type fullname="AddModItemAtStart" preserve="all" />
    <type fullname="AirWall" preserve="all" />
    <type fullname="AlchemyAltar" preserve="all" />
    <type fullname="AlchemyCardUI" preserve="all" />
    <type fullname="AlchemyLineTrack" preserve="all" />
    <type fullname="AnimationGravityData" preserve="all" />
    <type fullname="AttackData" preserve="all" />
    <type fullname="AttackHit" preserve="all" />
    <type fullname="AudioController" preserve="all" />
    <type fullname="AutoLetterbox.ForceCameraRatio" preserve="all" />
    <type fullname="AvatarData" preserve="all" />
    <type fullname="BasicInfoBar" preserve="all" />
    <type fullname="BehaviorDesigner.Runtime.BehaviorTree" preserve="all" />
    <type fullname="BehaviorDesigner.Runtime.ExternalBehaviorTree" preserve="all" />
    <type fullname="BigDialogueUI" preserve="all" />
    <type fullname="BirthCtrl" preserve="all" />
    <type fullname="BlackScreenTransform" preserve="all" />
    <type fullname="BookReadPoint" preserve="all" />
    <type fullname="BookUICtrl" preserve="all" />
    <type fullname="BossAiTest" preserve="all" />
    <type fullname="BossHpBarCtrl" preserve="all" />
    <type fullname="BossTimer" preserve="all" />
    <type fullname="BossUICtrl" preserve="all" />
    <type fullname="BreastFeedingCtrl" preserve="all" />
    <type fullname="BulletAimEnemy" preserve="all" />
    <type fullname="BulletAimPlayer" preserve="all" />
    <type fullname="BulletCallEvent" preserve="all" />
    <type fullname="BulletCircleAroundPlayer" preserve="all" />
    <type fullname="BulletOffsetPlayer" preserve="all" />
    <type fullname="BurstEffect" preserve="all" />
    <type fullname="ButtonSetting" preserve="all" />
    <type fullname="ButtonTipsUI" preserve="all" />
    <type fullname="CameraShake" preserve="all" />
    <type fullname="CanDashAniData" preserve="all" />
    <type fullname="CGCtrl" preserve="all" />
    <type fullname="CGCtrlSimple" preserve="all" />
    <type fullname="ChangeGuardSide" preserve="all" />
    <type fullname="CharacterCtrl" preserve="all" />
    <type fullname="CharacterMoveAsset" preserve="all" />
    <type fullname="CharacterState" preserve="all" />
    <type fullname="Chat" preserve="all" />
    <type fullname="ChatGroup" preserve="all" />
    <type fullname="ChestMonster" preserve="all" />
    <type fullname="ChestObj" preserve="all" />
    <type fullname="ComboBonusUI" preserve="all" />
    <type fullname="ComboData" preserve="all" />
    <type fullname="CounterattackBoxCtrl" preserve="all" />
    <type fullname="DamageFieldCtrl" preserve="all" />
    <type fullname="DamageNumCtrl" preserve="all" />
    <type fullname="DebugWindow" preserve="all" />
    <type fullname="DestroyOnRoomFlag" preserve="all" />
    <type fullname="DestructibleObj" preserve="all" />
    <type fullname="DialogueCharaCtrl" preserve="all" />
    <type fullname="DialogueCondition" preserve="all" />
    <type fullname="DialogueEnd" preserve="all" />
    <type fullname="DialogueEvent" preserve="all" />
    <type fullname="DialogueGraph" preserve="all" />
    <type fullname="DialogueRunScript" preserve="all" />
    <type fullname="DialogueSoundEvent" preserve="all" />
    <type fullname="DialogueSpeaker" preserve="all" />
    <type fullname="DialogueStartEvent" preserve="all" />
    <type fullname="DialogueUI" preserve="all" />
    <type fullname="DialogueWait" preserve="all" />
    <type fullname="DifficultyData" preserve="all" />
    <type fullname="DifficultyDisplayUI" preserve="all" />
    <type fullname="DifficultyUICtrl" preserve="all" />
    <type fullname="DisableOnKnockDown" preserve="all" />
    <type fullname="DPSTester" preserve="all" />
    <type fullname="DropItem" preserve="all" />
    <type fullname="DropItemOnDeath" preserve="all" />
    <type fullname="EffectByItem" preserve="all" />
    <type fullname="EffectSelfDestruction" preserve="all" />
    <type fullname="EffectSoundPlay" preserve="all" />
    <type fullname="Elevator" preserve="all" />
    <type fullname="EliteEffectData" preserve="all" />
    <type fullname="EliteEnemy" preserve="all" />
    <type fullname="EnemyBattleArea" preserve="all" />
    <type fullname="EnemyCtrl" preserve="all" />
    <type fullname="EnemyDeadByTime" preserve="all" />
    <type fullname="EnemyRecover" preserve="all" />
    <type fullname="EnemySpawn" preserve="all" />
    <type fullname="EnteractCGCtrl" preserve="all" />
    <type fullname="EnterToTransparent" preserve="all" />
    <type fullname="ExtraEnemyAssetData" preserve="all" />
    <type fullname="FlambergeFarAttack" preserve="all" />
    <type fullname="FmodSoundData" preserve="all" />
    <type fullname="FollowPlayer" preserve="all" />
    <type fullname="FontConfig" preserve="all" />
    <type fullname="FrostyAttacker" preserve="all" />
    <type fullname="FrostyEffect" preserve="all" />
    <type fullname="GameConfig" preserve="all" />
    <type fullname="GameManager" preserve="all" />
    <type fullname="GameTitleScene" preserve="all" />
    <type fullname="GoblinRideCtrl" preserve="all" />
    <type fullname="GuardianCtrl" preserve="all" />
    <type fullname="HAi" preserve="all" />
    <type fullname="HCameraCtrl" preserve="all" />
    <type fullname="HDeadEndCtrl" preserve="all" />
    <type fullname="HealAllEnemy" preserve="all" />
    <type fullname="HitBackUI" preserve="all" />
    <type fullname="HitBox" preserve="all" />
    <type fullname="HitComboCnt" preserve="all" />
    <type fullname="HolyEvilUI" preserve="all" />
    <type fullname="Hovl_Laser" preserve="all" />
    <type fullname="HStatusCtrl" preserve="all" />
    <type fullname="HTips" preserve="all" />
    <type fullname="HudResourceUI" preserve="all" />
    <type fullname="HudUI" preserve="all" />
    <type fullname="InputCtrl" preserve="all" />
    <type fullname="InputCtrlGirl" preserve="all" />
    <type fullname="InputHintSprite" preserve="all" />
    <type fullname="InputHintUI" preserve="all" />
    <type fullname="IronMaidenBabyCtrl" preserve="all" />
    <type fullname="IronMaidenCtrl" preserve="all" />
    <type fullname="ItemData" preserve="all" />
    <type fullname="JumpObj" preserve="all" />
    <type fullname="LeanTween" preserve="all" />
    <type fullname="LightAttacker" preserve="all" />
    <type fullname="LightCreator" preserve="all" />
    <type fullname="LoadingScene" preserve="all" />
    <type fullname="MapData" preserve="all" />
    <type fullname="MenuCtrl" preserve="all" />
    <type fullname="MinicBabyCtrl" preserve="all" />
    <type fullname="MiniMapUI" preserve="all" />
    <type fullname="MirzaBeig.ParticleSystems.AnimatedLight" preserve="all" />
    <type fullname="MirzaBeig.ParticleSystems.DestroyAfterTime" preserve="all" />
    <type fullname="MovePosition" preserve="all" />
    <type fullname="MoveToAttack" preserve="all" />
    <type fullname="ObjectRecord" preserve="all" />
    <type fullname="ObjectRecordManager" preserve="all" />
    <type fullname="ObjectTrigger" preserve="all" />
    <type fullname="ParallaxLayer" preserve="all" />
    <type fullname="ParticleAttackCheck" preserve="all" />
    <type fullname="particleAttractorLifeTime" preserve="all" />
    <type fullname="particleAttractorLinear" preserve="all" />
    <type fullname="particleAttractorSpherical" preserve="all" />
    <type fullname="PerfectEvadeBox" preserve="all" />
    <type fullname="PhilterAureole" preserve="all" />
    <type fullname="PickAntique" preserve="all" />
    <type fullname="PickFreeItem" preserve="all" />
    <type fullname="PickShopItem" preserve="all" />
    <type fullname="PictureTrapCtrl" preserve="all" />
    <type fullname="PlatformInfo" preserve="all" />
    <type fullname="PlayerAttackBehaviour" preserve="all" />
    <type fullname="PlayerAvatarCtrl" preserve="all" />
    <type fullname="PlayerCtrl" preserve="all" />
    <type fullname="PlayerGenerator" preserve="all" />
    <type fullname="PlayerRespawnCtrl" preserve="all" />
    <type fullname="PoisonInfection" preserve="all" />
    <type fullname="RoomClearUICtrl" preserve="all" />
    <type fullname="RoomInitObjGenerator" preserve="all" />
    <type fullname="SavingIcon" preserve="all" />
    <type fullname="SemenDropEffect" preserve="all" />
    <type fullname="SetPlayerSkin" preserve="all" />
    <type fullname="SetTextById" preserve="all" />
    <type fullname="SetTextFontByLanguage" preserve="all" />
    <type fullname="SettlementUI" preserve="all" />
    <type fullname="ShadowLightCtrl" preserve="all" />
    <type fullname="SheildGuard" preserve="all" />
    <type fullname="ShockWaveCtrl" preserve="all" />
    <type fullname="ShopRoom" preserve="all" />
    <type fullname="ShowMapName" preserve="all" />
    <type fullname="SimplePicCtrl" preserve="all" />
    <type fullname="SinDoorCtrl" preserve="all" />
    <type fullname="SinStatueCtrl" preserve="all" />
    <type fullname="SkeletonAnimationEvent" preserve="all" />
    <type fullname="SkeletonShadow" preserve="all" />
    <type fullname="SkillComboData" preserve="all" />
    <type fullname="SkillData" preserve="all" />
    <type fullname="SkillMagic" preserve="all" />
    <type fullname="SkillMagicLvUp" preserve="all" />
    <type fullname="SlugLarvaeCtrl" preserve="all" />
    <type fullname="SpiderLarvaeCtrl" preserve="all" />
    <type fullname="SpineAnimationPlayCtrl" preserve="all" />
    <type fullname="StagePointInfo" preserve="all" />
    <type fullname="StandDetailCtrl" preserve="all" />
    <type fullname="StandDrawCtrl" preserve="all" />
    <type fullname="StandDrawMod" preserve="all" />
    <type fullname="StartNode" preserve="all" />
    <type fullname="StateData" preserve="all" />
    <type fullname="StateEffectCtrl" preserve="all" />
    <type fullname="StateToAnimator" preserve="all" />
    <type fullname="StoryData" preserve="all" />
    <type fullname="StorytellingSplashEffect" preserve="all" />
    <type fullname="TextData" preserve="all" />
    <type fullname="TextPicCtrl" preserve="all" />
    <type fullname="TextTipCtrl" preserve="all" />
    <type fullname="TrailRenderTrigger" preserve="all" />
    <type fullname="TrapCtrl" preserve="all" />
    <type fullname="TreasureDropConfig" preserve="all" />
    <type fullname="TreasureRoom" preserve="all" />
    <type fullname="UnityEngine.UI.Extensions.CUIBezierCurve" preserve="all" />
    <type fullname="UnityEngine.UI.Extensions.TextPic" preserve="all" />
    <type fullname="UnityEngine.UI.Extensions.UI_TweenScale" preserve="all" />
    <type fullname="UnityEngine.UI.Extensions.UIPolygon" preserve="all" />
    <type fullname="UnityEngine.UI.Extensions.UISelectableExtension" preserve="all" />
    <type fullname="UpdateNewsUI" preserve="all" />
    <type fullname="UseItemEffect" preserve="all" />
    <type fullname="UseItemsData" preserve="all" />
    <type fullname="UseItemUI" preserve="all" />
    <type fullname="UVTextureAnimator" preserve="all" />
    <type fullname="VibrateData" preserve="all" />
    <type fullname="VirtualCameraSetPlayer" preserve="all" />
    <type fullname="Wardrobe" preserve="all" />
    <type fullname="WeaponData" preserve="all" />
    <type fullname="WeaponRackCtrl" preserve="all" />
    <type fullname="ChatGroup/DialogueGroup" preserve="nothing" serialized="true" />
    <type fullname="DialogueCondition/Condition" preserve="nothing" serialized="true" />
    <type fullname="TreasureDropConfig/TreasureDropDetail" preserve="nothing" serialized="true" />
    <type fullname="MainStates" preserve="nothing" serialized="true" />
    <type fullname="MoveToAttack/EnemyAttackType" preserve="nothing" serialized="true" />
    <type fullname="PregSeed" preserve="nothing" serialized="true" />
    <type fullname="Achievement" preserve="nothing" serialized="true" />
    <type fullname="AnimationGravityData/AnimationGravityDetail" preserve="nothing" serialized="true" />
    <type fullname="Attack" preserve="nothing" serialized="true" />
    <type fullname="Chat/Answer" preserve="nothing" serialized="true" />
    <type fullname="Combo" preserve="nothing" serialized="true" />
    <type fullname="Difficulty" preserve="nothing" serialized="true" />
    <type fullname="EliteProperty" preserve="nothing" serialized="true" />
    <type fullname="FmodStringToEvent" preserve="nothing" serialized="true" />
    <type fullname="GText" preserve="nothing" serialized="true" />
    <type fullname="Item" preserve="nothing" serialized="true" />
    <type fullname="Map" preserve="nothing" serialized="true" />
    <type fullname="ModItem" preserve="nothing" serialized="true" />
    <type fullname="PlayerAvatar" preserve="nothing" serialized="true" />
    <type fullname="Skill" preserve="nothing" serialized="true" />
    <type fullname="SkillCombo" preserve="nothing" serialized="true" />
    <type fullname="State" preserve="nothing" serialized="true" />
    <type fullname="StoryDetail" preserve="nothing" serialized="true" />
    <type fullname="UseItem" preserve="nothing" serialized="true" />
    <type fullname="WeaponData/WeaponDetail" preserve="nothing" serialized="true" />
    <type fullname="StandDrawMod/ModAttachment" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Extensions.TextPic/HrefClickEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Extensions.TextPic/IconName" preserve="nothing" serialized="true" />
    <type fullname="SplashEffectData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Extensions.UISelectableExtension/UIButtonEvent" preserve="nothing" serialized="true" />
    <type fullname="AutoLetterbox.CameraRatio" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Cinemachine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Cinemachine.CinemachineBasicMultiChannelPerlin" preserve="all" />
    <type fullname="Cinemachine.CinemachineBlenderSettings" preserve="all" />
    <type fullname="Cinemachine.CinemachineBlendListCamera" preserve="all" />
    <type fullname="Cinemachine.CinemachineBrain" preserve="all" />
    <type fullname="Cinemachine.CinemachineComposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineConfiner" preserve="all" />
    <type fullname="Cinemachine.CinemachineFramingTransposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineImpulseListener" preserve="all" />
    <type fullname="Cinemachine.CinemachineImpulseSource" preserve="all" />
    <type fullname="Cinemachine.CinemachinePipeline" preserve="all" />
    <type fullname="Cinemachine.CinemachineSmoothPath" preserve="all" />
    <type fullname="Cinemachine.CinemachineTargetGroup" preserve="all" />
    <type fullname="Cinemachine.CinemachineTransposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineVirtualCamera" preserve="all" />
    <type fullname="Cinemachine.NoiseSettings" preserve="all" />
    <type fullname="Cinemachine.PostFX.CinemachinePostProcessing" preserve="all" />
    <type fullname="Cinemachine.PostFX.CinemachineVolumeSettings" preserve="all" />
    <type fullname="CinemachineShot" preserve="all" />
    <type fullname="CinemachineTrack" preserve="all" />
    <type fullname="Cinemachine.CinemachineBlendDefinition" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBlenderSettings/CustomBlend" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.NoiseSettings/NoiseParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.NoiseSettings/TransformNoiseParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBrain/VcamActivatedEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineVirtualCameraBase/TransitionParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.LensSettings" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineImpulseDefinition" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineImpulseManager/EnvelopeDefinition" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBrain/BrainEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineTargetGroup/Target" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachinePathBase/Appearance" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineSmoothPath/Waypoint" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBlendListCamera/Instruction" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Coffee.UIParticle, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Coffee.UIExtensions.UIParticle" preserve="all" />
  </assembly>
  <assembly fullname="FMODUnity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="FMODUnity.FMODEventPlayable" preserve="all" />
    <type fullname="FMODUnity.FMODEventTrack" preserve="all" />
    <type fullname="FMODUnity.StudioEventEmitter" preserve="all" />
    <type fullname="FMODUnity.StudioListener" preserve="all" />
    <type fullname="FMOD.GUID" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.EventReference" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.ParamRef" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.AutomatableSlots" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.FMODEventMixerBehaviour" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.FMODEventPlayableBehavior" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="spine-unity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Spine.Unity.ActivateBasedOnFlipDirection" preserve="all" />
    <type fullname="Spine.Unity.BoneFollower" preserve="all" />
    <type fullname="Spine.Unity.BoneFollowerGraphic" preserve="all" />
    <type fullname="Spine.Unity.BoundingBoxFollower" preserve="all" />
    <type fullname="Spine.Unity.FollowLocationRigidbody2D" preserve="all" />
    <type fullname="Spine.Unity.PointFollower" preserve="all" />
    <type fullname="Spine.Unity.SkeletonAnimation" preserve="all" />
    <type fullname="Spine.Unity.SkeletonDataAsset" preserve="all" />
    <type fullname="Spine.Unity.SkeletonGraphic" preserve="all" />
    <type fullname="Spine.Unity.SkeletonGraphicCustomMaterials" preserve="all" />
    <type fullname="Spine.Unity.SkeletonMecanim" preserve="all" />
    <type fullname="Spine.Unity.SkeletonMecanimRootMotion" preserve="all" />
    <type fullname="Spine.Unity.SkeletonPartsRenderer" preserve="all" />
    <type fullname="Spine.Unity.SkeletonRenderer" preserve="all" />
    <type fullname="Spine.Unity.SkeletonRendererCustomMaterials" preserve="all" />
    <type fullname="Spine.Unity.SkeletonRenderSeparator" preserve="all" />
    <type fullname="Spine.Unity.SkeletonRootMotion" preserve="all" />
    <type fullname="Spine.Unity.SkeletonSubmeshGraphic" preserve="all" />
    <type fullname="Spine.Unity.SkeletonUtility" preserve="all" />
    <type fullname="Spine.Unity.SkeletonUtilityBone" preserve="all" />
    <type fullname="Spine.Unity.SpineAtlasAsset" preserve="all" />
    <type fullname="Spine.Unity.BlendModeMaterials" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.BlendModeMaterials/ReplacementMaterial" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.SkeletonMecanim/MecanimTranslator" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.SkeletonRenderer/SpriteMaskInteractionMaterials" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.MeshGenerator" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.MeshGenerator/Settings" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.SkeletonGraphicCustomMaterials/AtlasMaterialOverride" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.SkeletonGraphicCustomMaterials/AtlasTextureOverride" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.SkeletonRendererCustomMaterials/AtlasMaterialOverride" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="spine-unity-examples, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Spine.Unity.Examples.MecanimToAnimationHandleExample" preserve="all" />
    <type fullname="Spine.Unity.Examples.SkeletonGhost" preserve="all" />
  </assembly>
  <assembly fullname="UIEffect, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Coffee.UIEffects.UIDissolve" preserve="all" />
    <type fullname="Coffee.UIEffects.UIEffect" preserve="all" />
    <type fullname="Coffee.UIEffects.UIShiny" preserve="all" />
    <type fullname="Coffee.UIEffects.UISyncEffect" preserve="all" />
    <type fullname="Coffee.UIEffects.UITransitionEffect" preserve="all" />
    <type fullname="Coffee.UIEffects.EffectPlayer" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.2D.SpriteShape.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.U2D.SpriteShape" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteShapeController" preserve="all" />
    <type fullname="UnityEngine.U2D.AngleRange" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.U2D.CornerSprite" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.U2D.Spline" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.U2D.SplineControlPoint" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
    <type fullname="UnityEngine.AddressableAssets.AssetReferenceT`1[AnimationGravityData]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.AddressableAssets.AssetReferenceT`1[CanDashAniData]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.AddressableAssets.AssetReferenceT`1[SkillComboData]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.AddressableAssets.AssetReferenceGameObject" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.AddressableAssets.AssetReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.InputSystem, Version=*******, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.InputSystem.InputActionAsset" preserve="all" />
    <type fullname="UnityEngine.InputSystem.InputActionReference" preserve="all" />
    <type fullname="UnityEngine.InputSystem.UI.InputSystemUIInputModule" preserve="all" />
    <type fullname="UnityEngine.InputSystem.InputAction" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.InputSystem.InputActionMap" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.InputSystem.InputBinding" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.InputSystem.InputControlScheme" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.InputSystem.InputControlScheme/DeviceRequirement" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.Volume" preserve="all" />
    <type fullname="UnityEngine.Rendering.VolumeProfile" preserve="all" />
    <type fullname="UnityEngine.Rendering.BoolParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ClampedFloatParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ClampedIntParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ColorParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.MinFloatParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.TextureParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.FloatParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.NoInterpTextureParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Vector2Parameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Vector4Parameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.TextureCurve" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.TextureCurveParameter" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Experimental.Rendering.Universal.Light2D" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.Bloom" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.ChromaticAberration" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.ColorAdjustments" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.ColorCurves" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.DepthOfField" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.FilmGrain" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.LensDistortion" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.LiftGammaGain" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.MotionBlur" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.PaniniProjection" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.ShadowsMidtonesHighlights" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.SplitToning" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.Tonemapping" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalCameraData" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.Vignette" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.WhiteBalance" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.DepthOfFieldModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Universal.FilmGrainLookupParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Universal.MotionBlurModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Universal.MotionBlurQualityParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Universal.TonemappingModeParameter" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AtlasSpriteProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshPro" preserve="all" />
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphValueRecord" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Timeline, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Timeline.ActivationPlayableAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.ActivationTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.AnimationTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.ControlPlayableAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.ControlTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.MarkerTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.PlayableTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.SignalAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.SignalEmitter" preserve="all" />
    <type fullname="UnityEngine.Timeline.SignalReceiver" preserve="all" />
    <type fullname="UnityEngine.Timeline.SignalTrack" preserve="all" />
    <type fullname="UnityEngine.Timeline.TimelineAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.MarkerList" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.TimelineAsset/EditorSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.TimelineClip" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.SignalReceiver/EventKeyValue" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.AnimatorOverrideController" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AudioListener" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.LightmapSettings" preserve="all" />
    <type fullname="UnityEngine.LineRenderer" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.Rendering.SortingGroup" preserve="all" />
    <type fullname="UnityEngine.RenderSettings" preserve="all" />
    <type fullname="UnityEngine.RenderTexture" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.SpriteRenderer" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.TrailRenderer" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.ExposedReference`1[Cinemachine.CinemachineVirtualCameraBase]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.ExposedReference`1[UnityEngine.GameObject]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.ExposedReference`1[UnityEngine.Animator]" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.DirectorModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Playables.PlayableDirector" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.Physics2DModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider2D" preserve="all" />
    <type fullname="UnityEngine.CapsuleCollider2D" preserve="all" />
    <type fullname="UnityEngine.CircleCollider2D" preserve="all" />
    <type fullname="UnityEngine.EdgeCollider2D" preserve="all" />
    <type fullname="UnityEngine.HingeJoint2D" preserve="all" />
    <type fullname="UnityEngine.PhysicsMaterial2D" preserve="all" />
    <type fullname="UnityEngine.PlatformEffector2D" preserve="all" />
    <type fullname="UnityEngine.PolygonCollider2D" preserve="all" />
    <type fullname="UnityEngine.Rigidbody2D" preserve="all" />
    <type fullname="UnityEngine.SpringJoint2D" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.SpriteMaskModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.SpriteMask" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.SpriteShapeModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.U2D.SpriteShapeRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.EventSystems.EventSystem" preserve="all" />
    <type fullname="UnityEngine.EventSystems.EventTrigger" preserve="all" />
    <type fullname="UnityEngine.UI.Button" preserve="all" />
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.Outline" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.Scrollbar" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.Selectable" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="BehaviorDesigner.Runtime">
    <type fullname="BehaviorDesigner.Runtime.BehaviorSource" preserve="nothing" serialized="true" />
    <type fullname="BehaviorDesigner.Runtime.FieldSerializationData" preserve="nothing" serialized="true" />
    <type fullname="BehaviorDesigner.Runtime.TaskSerializationData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Mathematics">
    <type fullname="Unity.Mathematics.int2" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="XNode">
    <type fullname="XNode.Node/NodePortDictionary" preserve="nothing" serialized="true" />
    <type fullname="XNode.NodePort" preserve="nothing" serialized="true" />
    <type fullname="XNode.NodePort/PortConnection" preserve="nothing" serialized="true" />
  </assembly>
</linker>