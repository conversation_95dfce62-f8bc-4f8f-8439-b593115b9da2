#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ThornSin音频提取工具
专门用于提取和分析游戏音频资源
"""

import os
import struct
import shutil
from pathlib import Path

def extract_fmod_bank(bank_file, output_dir):
    """尝试从FMOD bank文件中提取音频"""
    print(f"\n提取文件: {bank_file}")
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    try:
        with open(bank_file, 'rb') as f:
            data = f.read()
            
            # 查找常见音频格式的魔数
            audio_formats = {
                b'OggS': '.ogg',
                b'RIFF': '.wav', 
                b'ID3': '.mp3',
                b'\xFF\xFB': '.mp3',
                b'\xFF\xFA': '.mp3',
                b'fLaC': '.flac',
                b'FSB5': '.fsb'
            }
            
            found_audio = []
            
            for i in range(len(data) - 4):
                for magic, ext in audio_formats.items():
                    if data[i:i+len(magic)] == magic:
                        # 找到音频文件头
                        print(f"在偏移 {i:08X} 处找到 {ext} 格式")
                        
                        # 尝试确定文件大小
                        if ext == '.ogg':
                            # OGG文件处理
                            end_pos = data.find(b'OggS', i + 4)
                            if end_pos == -1:
                                end_pos = len(data)
                            
                            audio_data = data[i:end_pos]
                            filename = f"audio_{i:08X}{ext}"
                            
                        elif ext == '.wav' and len(data) >= i + 8:
                            # WAV文件处理
                            try:
                                size = struct.unpack('<I', data[i+4:i+8])[0]
                                audio_data = data[i:i+8+size]
                                filename = f"audio_{i:08X}{ext}"
                            except:
                                continue
                                
                        else:
                            # 其他格式，尝试找到下一个音频文件或文件结尾
                            next_audio = len(data)
                            for next_magic, _ in audio_formats.items():
                                next_pos = data.find(next_magic, i + len(magic))
                                if next_pos != -1 and next_pos < next_audio:
                                    next_audio = next_pos
                            
                            audio_data = data[i:next_audio]
                            filename = f"audio_{i:08X}{ext}"
                        
                        # 保存音频文件
                        if len(audio_data) > 1024:  # 只保存大于1KB的文件
                            output_file = output_path / filename
                            with open(output_file, 'wb') as af:
                                af.write(audio_data)
                            found_audio.append(filename)
                            print(f"  保存: {filename} ({len(audio_data)} bytes)")
            
            print(f"从 {bank_file.name} 提取了 {len(found_audio)} 个音频文件")
            return found_audio
            
    except Exception as e:
        print(f"提取失败: {e}")
        return []

def analyze_music_files():
    """分析可能的BGM文件"""
    print("\n分析可能的BGM文件...")
    
    # 基于文件名和大小推测BGM
    music_candidates = [
        "MUS.bank",  # 最可能包含BGM的文件
        "AMB.bank",  # 可能包含环境音乐
    ]
    
    base_path = Path(".")
    streaming_assets = base_path / "ThornSin_Data" / "StreamingAssets"
    
    for candidate in music_candidates:
        bank_file = streaming_assets / candidate
        if bank_file.exists():
            print(f"\n分析 {candidate}:")
            print(f"文件大小: {bank_file.stat().st_size:,} bytes")
            
            # 创建输出目录
            output_dir = f"extracted_audio/{candidate.replace('.bank', '')}"
            extracted = extract_fmod_bank(bank_file, output_dir)
            
            if extracted:
                print(f"成功提取 {len(extracted)} 个音频文件到 {output_dir}")

def search_secret_passage_bgm():
    """搜索可能的secret passage BGM"""
    print("\n搜索Secret Passage关卡BGM...")
    
    # 读取字符串文件，寻找相关线索
    strings_file = Path("fmod_strings_all.txt")
    if strings_file.exists():
        with open(strings_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read().lower()
            
        # 搜索可能的关键词
        keywords = ['secret', 'passage', 'cave', 'dungeon', 'underground', 'hidden']
        
        print("搜索相关关键词:")
        for keyword in keywords:
            if keyword in content:
                print(f"  找到关键词: {keyword}")
            else:
                print(f"  未找到: {keyword}")
    
    # 基于游戏结构推测
    print("\n基于游戏结构推测:")
    print("Secret Passage通常是:")
    print("1. 洞穴/地下场景 - 可能使用Cave相关BGM")
    print("2. 隐藏区域 - 可能使用特殊的环境音乐")
    print("3. 过渡区域 - 可能使用较为安静的背景音乐")
    
    # 推荐的BGM候选
    bgm_candidates = [
        "Cave相关音乐 - 适合地下/洞穴场景",
        "Forest相关音乐 - 如果是森林中的秘密通道",
        "Ambience相关音乐 - 环境音效",
        "Stage相关音乐 - 特定关卡音乐"
    ]
    
    print("\n推荐的BGM候选:")
    for i, candidate in enumerate(bgm_candidates, 1):
        print(f"{i}. {candidate}")

def main():
    print("ThornSin音频提取工具")
    print("=" * 50)
    
    # 创建输出目录
    output_base = Path("extracted_audio")
    output_base.mkdir(exist_ok=True)
    
    # 分析和提取音频文件
    analyze_music_files()
    
    # 搜索secret passage BGM
    search_secret_passage_bgm()
    
    print("\n提取完成！")
    print("请检查 extracted_audio 文件夹中的音频文件")

if __name__ == "__main__":
    main()
