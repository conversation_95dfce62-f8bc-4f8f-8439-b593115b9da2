#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找Secret Passage关卡BGM的智能分析工具
"""

import os
from pathlib import Path
import struct

def analyze_audio_file(file_path):
    """分析音频文件的基本信息"""
    try:
        size = file_path.stat().st_size
        
        # 读取文件头部信息
        with open(file_path, 'rb') as f:
            header = f.read(32)
        
        # 判断文件类型和质量
        file_type = "unknown"
        if header.startswith(b'ID3') or header[1:4] == b'\xFF\xFB':
            file_type = "mp3"
        elif header.startswith(b'RIFF'):
            file_type = "wav"
        elif header.startswith(b'OggS'):
            file_type = "ogg"
        
        # 基于文件大小推测用途
        purpose = "unknown"
        if size < 5000:
            purpose = "sound_effect"
        elif size < 50000:
            purpose = "voice_or_short_music"
        elif size < 200000:
            purpose = "background_music"
        else:
            purpose = "long_background_music"
        
        return {
            'size': size,
            'type': file_type,
            'purpose': purpose,
            'header': header[:8].hex()
        }
    except Exception as e:
        return {'error': str(e)}

def find_bgm_candidates():
    """寻找可能的BGM候选文件"""
    print("分析提取的音频文件，寻找Secret Passage BGM候选...")
    
    base_path = Path("extracted_audio")
    candidates = []
    
    # 分析MUS文件夹 (音乐库)
    mus_path = base_path / "MUS"
    if mus_path.exists():
        print(f"\n分析 MUS 文件夹...")
        mus_files = list(mus_path.glob("*.mp3"))
        
        for file_path in mus_files:
            info = analyze_audio_file(file_path)
            if info.get('purpose') in ['background_music', 'long_background_music']:
                candidates.append({
                    'file': file_path,
                    'source': 'MUS',
                    'info': info
                })
    
    # 分析AMB文件夹 (环境音效库)
    amb_path = base_path / "AMB"
    if amb_path.exists():
        print(f"分析 AMB 文件夹...")
        amb_files = list(amb_path.glob("*.mp3"))
        
        for file_path in amb_files:
            info = analyze_audio_file(file_path)
            if info.get('purpose') in ['background_music', 'long_background_music']:
                candidates.append({
                    'file': file_path,
                    'source': 'AMB',
                    'info': info
                })
    
    return candidates

def rank_candidates(candidates):
    """对候选文件进行排序和评分"""
    print(f"\n找到 {len(candidates)} 个BGM候选文件")
    
    # 按文件大小和来源进行评分
    scored_candidates = []
    
    for candidate in candidates:
        score = 0
        info = candidate['info']
        
        # 文件大小评分
        size = info.get('size', 0)
        if 50000 <= size <= 500000:  # 50KB-500KB 适合BGM
            score += 10
        elif size > 500000:  # 超大文件可能是长BGM
            score += 8
        elif size > 20000:  # 中等文件可能是短BGM
            score += 5
        
        # 来源评分
        if candidate['source'] == 'MUS':  # 音乐库更可能包含BGM
            score += 5
        elif candidate['source'] == 'AMB':  # 环境音效库可能包含氛围音乐
            score += 3
        
        # 文件偏移地址分析 (某些地址可能对应特定场景)
        filename = candidate['file'].name
        if 'audio_' in filename:
            try:
                offset = int(filename.split('_')[1].split('.')[0], 16)
                # 某些偏移地址范围可能对应特定类型的音频
                if 0x100000 <= offset <= 0x300000:  # 中等偏移可能是关卡音乐
                    score += 2
            except:
                pass
        
        scored_candidates.append({
            **candidate,
            'score': score
        })
    
    # 按评分排序
    scored_candidates.sort(key=lambda x: x['score'], reverse=True)
    return scored_candidates

def main():
    print("Secret Passage BGM 智能查找工具")
    print("=" * 50)
    
    # 寻找候选文件
    candidates = find_bgm_candidates()
    
    if not candidates:
        print("未找到合适的BGM候选文件")
        return
    
    # 对候选文件进行排序
    ranked_candidates = rank_candidates(candidates)
    
    # 显示前20个最可能的候选文件
    print("\n最可能的Secret Passage BGM候选文件 (前20个):")
    print("-" * 80)
    print(f"{'排名':<4} {'文件名':<25} {'来源':<6} {'大小':<10} {'类型':<8} {'评分':<4}")
    print("-" * 80)
    
    for i, candidate in enumerate(ranked_candidates[:20], 1):
        file_path = candidate['file']
        info = candidate['info']
        filename = file_path.name
        source = candidate['source']
        size = f"{info.get('size', 0):,}B"
        purpose = info.get('purpose', 'unknown')
        score = candidate['score']
        
        print(f"{i:<4} {filename:<25} {source:<6} {size:<10} {purpose:<8} {score:<4}")
    
    # 提供具体建议
    print("\n" + "=" * 50)
    print("建议:")
    print("1. 优先试听评分最高的文件")
    print("2. 关注来自MUS库的较大文件 (>50KB)")
    print("3. 寻找具有循环特征的音乐文件")
    print("4. 注意氛围较为神秘/幽暗的音乐")
    
    # 生成播放列表
    playlist_file = "secret_passage_bgm_candidates.txt"
    with open(playlist_file, 'w', encoding='utf-8') as f:
        f.write("Secret Passage BGM 候选文件列表\n")
        f.write("=" * 40 + "\n\n")
        
        for i, candidate in enumerate(ranked_candidates[:10], 1):
            file_path = candidate['file']
            info = candidate['info']
            f.write(f"{i}. {file_path}\n")
            f.write(f"   大小: {info.get('size', 0):,} bytes\n")
            f.write(f"   评分: {candidate['score']}\n")
            f.write(f"   建议: {info.get('purpose', 'unknown')}\n\n")
    
    print(f"\n候选文件列表已保存到: {playlist_file}")

if __name__ == "__main__":
    main()
